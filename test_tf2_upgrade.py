#!/usr/bin/env python3
"""
Test script to verify TF2 upgrade functionality
"""

import os
import sys
import numpy as np
import tensorflow as tf

# Add current directory to path
sys.path.append('.')

try:
    import net_CTU64 as nt
    import input_data
    print("✓ Successfully imported modules")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)

def test_basic_functionality():
    """Test basic functionality of upgraded modules"""
    print("\n=== Testing Basic Functionality ===")
    
    # Test constants
    print(f"IMAGE_SIZE: {nt.IMAGE_SIZE}")
    print(f"NUM_CHANNELS: {nt.NUM_CHANNELS}")
    print(f"NUM_EXT_FEATURES: {nt.NUM_EXT_FEATURES}")
    
    # Test weight variable creation
    try:
        w = nt.weight_variable([3, 3, 1, 16], name="test_weight")
        print(f"✓ Weight variable created: {w.shape}")
    except Exception as e:
        print(f"✗ Weight variable creation failed: {e}")
        return False
    
    # Test bias variable creation
    try:
        b = nt.bias_variable([16], name="test_bias")
        print(f"✓ Bias variable created: {b.shape}")
    except Exception as e:
        print(f"✗ Bias variable creation failed: {e}")
        return False
    
    return True

def test_network_functions():
    """Test network utility functions"""
    print("\n=== Testing Network Functions ===")
    
    # Create dummy input
    x = tf.random.normal([2, 64, 64, 1])
    
    try:
        # Test pooling functions
        pooled = nt.aver_pool(x, 2)
        print(f"✓ Average pooling: {x.shape} -> {pooled.shape}")
        
        # Test activation function
        activated = nt.activate(x, 5)  # leaky_relu
        print(f"✓ Activation function works")
        
        # Test normalization
        normalized = nt.zero_mean_norm_global(x)
        print(f"✓ Global normalization works")
        
        return True
    except Exception as e:
        print(f"✗ Network function test failed: {e}")
        return False

def test_net_function():
    """Test the main net function"""
    print("\n=== Testing Main Net Function ===")

    try:
        # Create dummy inputs
        batch_size = 2
        x = tf.random.normal([batch_size, nt.IMAGE_SIZE, nt.IMAGE_SIZE, nt.NUM_CHANNELS])
        y_ = tf.random.uniform([batch_size, nt.NUM_LABEL_BYTES], maxval=4, dtype=tf.float32)
        qp = tf.random.uniform([batch_size, nt.NUM_EXT_FEATURES], minval=20, maxval=40, dtype=tf.float32)
        isdrop = tf.constant(0.0)
        global_step = tf.constant(1.0)

        # Test net function
        results = nt.net(x, y_, qp, isdrop, global_step, 0.01, 0.9, 1000, 0.5)

        print(f"✓ Net function executed successfully")
        print(f"  - Number of outputs: {len(results)}")
        print(f"  - y_conv_64 shape: {results[3].shape}")
        print(f"  - y_conv_32 shape: {results[4].shape}")
        print(f"  - y_conv_16 shape: {results[5].shape}")
        print(f"  - Total loss: {results[6]}")

        return True
    except Exception as e:
        print(f"✗ Net function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_functions():
    """Test the TF2 training functions"""
    print("\n=== Testing TF2 Training Functions ===")

    try:
        # Import training module
        import train_CNN_CTU64 as train_module

        # Create model components
        train_module.create_model_and_optimizer()

        # Create dummy inputs
        batch_size = 2
        x = tf.random.normal([batch_size, nt.IMAGE_SIZE, nt.IMAGE_SIZE, nt.NUM_CHANNELS]) * 255.0
        y_ = tf.random.uniform([batch_size, nt.NUM_LABEL_BYTES], maxval=4, dtype=tf.float32)
        qp = tf.random.uniform([batch_size, nt.NUM_EXT_FEATURES], minval=20, maxval=40, dtype=tf.float32)

        # Test forward pass
        results = train_module.predict_step(x, y_, qp, isdrop=0.0)
        print(f"✓ Forward pass works")
        print(f"  - y_conv_64 shape: {results['y_conv_64'].shape}")
        print(f"  - y_conv_32 shape: {results['y_conv_32'].shape}")
        print(f"  - y_conv_16 shape: {results['y_conv_16'].shape}")
        print(f"  - Total loss: {results['total_loss']}")

        # Test training step
        train_results = train_module.train_step(x, y_, qp, isdrop=1.0)
        print(f"✓ Training step works")
        print(f"  - Learning rate: {train_results['learning_rate']}")

        return True
    except Exception as e:
        print(f"✗ Training functions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loading():
    """Test data loading functionality"""
    print("\n=== Testing Data Loading ===")
    
    try:
        # Test if data files exist
        data_dir = 'Data'
        if not os.path.exists(data_dir):
            print(f"⚠ Data directory '{data_dir}' not found, skipping data loading test")
            return True
        
        # Check for demo files
        demo_files = ['AI_Train_5000.dat_shuffled', 'AI_Valid_5000.dat_shuffled', 'AI_Test_5000.dat_shuffled']
        missing_files = [f for f in demo_files if not os.path.exists(os.path.join(data_dir, f))]
        
        if missing_files:
            print(f"⚠ Missing data files: {missing_files}, skipping data loading test")
            return True
        
        # Test data loading
        data_sets = input_data.read_data_sets()
        print(f"✓ Data sets loaded successfully")
        print(f"  - Train samples: {data_sets.train.num_examples}")
        print(f"  - Valid samples: {data_sets.validation.num_examples}")
        print(f"  - Test samples: {data_sets.test.num_examples}")
        
        # Test batch generation
        batch = data_sets.train.next_batch_random(4)
        print(f"✓ Batch generation works: {len(batch)} arrays")
        print(f"  - Images shape: {batch[0].shape}")
        print(f"  - Labels shape: {batch[1].shape}")
        print(f"  - QPs shape: {batch[2].shape}")
        
        return True
    except Exception as e:
        print(f"✗ Data loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("TensorFlow 2 Upgrade Verification Test")
    print("=" * 40)
    print(f"TensorFlow version: {tf.__version__}")
    print(f"GPU available: {tf.config.list_physical_devices('GPU')}")
    
    tests = [
        test_basic_functionality,
        test_network_functions,
        test_net_function,
        test_training_functions,
        test_data_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} failed")
        except Exception as e:
            print(f"✗ {test.__name__} crashed: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! TF2 upgrade appears successful.")
        return 0
    else:
        print("❌ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
